# Import required libraries for data analysis and visualization
import pandas as pd  # For data manipulation and analysis
import matplotlib.pyplot as plt  # For creating visualizations and plots

# Load the Netflix dataset from CSV file
# This dataset contains comprehensive information about movies and TV shows available on Netflix
# Expected columns: show_id, type, title, director, cast, country, date_added, release_year, duration, description, genre
netflix_df = pd.read_csv("netflix_data.csv")

# Display confirmation message and basic dataset information
print("✅ Dataset loaded successfully!")
print(f"📊 Dataset shape: {netflix_df.shape[0]} rows × {netflix_df.shape[1]} columns")
print(f"💾 Memory usage: {netflix_df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Display comprehensive information about the dataset structure
# The .info() method provides data types, non-null counts, and memory usage
print("📋 Dataset Information:")
print("=" * 50)
netflix_df.info()

# Analyze data quality and provide summary insights
print("\n🎯 Key Observations:")
print(f"• Total entries: {len(netflix_df):,}")
print(f"• No missing values detected (all columns show {len(netflix_df)} non-null entries)")
print(f"• Mix of categorical (object) and numerical (int64) data types")
print(f"• Dataset appears clean and ready for analysis")

# Display first few rows to understand data structure
print("\n📝 Sample Data Preview:")
print(netflix_df.head(3).to_string())

# Filter the dataset for content released in the 1990s (1990-1999)
# Using boolean indexing with logical AND (&) operator to create a subset
# This creates a mask that selects rows where release_year is between 1990-1999 (inclusive)
nineties_content = netflix_df[
    (netflix_df['release_year'] >= 1990) & 
    (netflix_df['release_year'] < 2000)  # Using < 2000 instead of <= 1999 for clarity
]

# Calculate and display summary statistics for our filtered dataset
total_nineties = len(nineties_content)
total_catalog = len(netflix_df)
percentage_nineties = (total_nineties / total_catalog) * 100

print(f"🎭 1990s Content Analysis:")
print(f"📊 Total 1990s content: {total_nineties:,} items")
print(f"📈 Percentage of total catalog: {percentage_nineties:.1f}%")
print(f"🎯 This represents a significant portion of Netflix's nostalgic content")

# Display sample data to verify our filtering worked correctly
print("\n🔍 Sample of 1990s content (showing release years to verify filter):")
sample_data = nineties_content[['title', 'release_year', 'type', 'genre']].head()
print(sample_data.to_string())

# Return the full dataframe for further analysis
nineties_content.head()

# Filter to include only movies (excluding TV shows) from our 1990s dataset
# This step is crucial as movie duration analysis doesn't apply to TV shows
nineties_movies = nineties_content[nineties_content['type'] == 'Movie']

# Calculate key metrics for our refined dataset
total_movies = len(nineties_movies)
total_tv_shows = len(nineties_content) - total_movies
movie_percentage = (total_movies / len(nineties_content)) * 100

# Display comprehensive summary statistics
print(f"🎬 1990s Movies Analysis:")
print(f"📊 Total 1990s movies: {total_movies:,}")
print(f"📺 TV shows excluded: {total_tv_shows:,}")
print(f"🎯 Movies represent {movie_percentage:.1f}% of 1990s content")

# Analyze genre distribution to understand content variety
print(f"\n🎭 Genre Distribution Analysis (Top 5):")
genre_counts = nineties_movies['genre'].value_counts().head()
for genre, count in genre_counts.items():
    percentage = (count / total_movies) * 100
    print(f"   • {genre}: {count} movies ({percentage:.1f}%)")

# Store the movie dataset for further analysis
# This will be our primary dataset for duration and action movie analysis
print("\n🔍 Sample of 1990s movies (first 5 entries):")
sample_display = nineties_movies[['title', 'release_year', 'duration', 'genre', 'director']].head()
print(sample_display.to_string())

# Return the full dataframe for Jupyter display
nineties_movies.head()

# Create a histogram to visualize the distribution of movie durations
# Using matplotlib to create a clear, informative visualization

# Set up the plot with appropriate figure size for better readability
plt.figure(figsize=(10, 6))

# Create histogram with customized bins for better granularity
# Using 20 bins to show detailed distribution patterns
plt.hist(nineties_movies["duration"], bins=20, color='#E50914', alpha=0.7, edgecolor='black')

# Add comprehensive labels and title
plt.title('Distribution of 1990s Movie Durations on Netflix', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Duration (minutes)', fontsize=12)
plt.ylabel('Number of Movies', fontsize=12)

# Add grid for better readability
plt.grid(True, alpha=0.3)

# Calculate and display key statistics on the plot
mean_duration = nineties_movies['duration'].mean()
median_duration = nineties_movies['duration'].median()

# Add vertical lines for mean and median
plt.axvline(mean_duration, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_duration:.1f} min')
plt.axvline(median_duration, color='blue', linestyle='--', linewidth=2, label=f'Median: {median_duration:.1f} min')

# Add legend and display statistics
plt.legend()
plt.tight_layout()

# Display the plot
plt.show()

# Print summary statistics for detailed analysis
print(f"📊 Duration Statistics for 1990s Movies:")
print(f"   • Mean duration: {mean_duration:.1f} minutes")
print(f"   • Median duration: {median_duration:.1f} minutes")
print(f"   • Shortest movie: {nineties_movies['duration'].min()} minutes")
print(f"   • Longest movie: {nineties_movies['duration'].max()} minutes")
print(f"   • Standard deviation: {nineties_movies['duration'].std():.1f} minutes")

# Define a threshold for analyzing short vs. long movies
# Based on our histogram analysis, 100 minutes appears to be around the median
duration_threshold = 100

# This threshold will be used to categorize movies and identify patterns
print(f"📏 Duration threshold set to: {duration_threshold} minutes")
print(f"🎯 This will help us categorize movies as 'short' (≤{duration_threshold} min) or 'long' (>{duration_threshold} min)")

# Filter for action movies from our 1990s movie dataset
# This will help us analyze the specific characteristics of 90s action films
action_movies_90s = nineties_movies[nineties_movies['genre'] == 'Action']

# Calculate comprehensive statistics for action movies
total_action_movies = len(action_movies_90s)
action_percentage = (total_action_movies / len(nineties_movies)) * 100
avg_action_duration = action_movies_90s['duration'].mean()

# Display detailed analysis of action movies
print(f"🎬 1990s Action Movies Analysis:")
print(f"📊 Total action movies: {total_action_movies}")
print(f"📈 Percentage of 90s movies: {action_percentage:.1f}%")
print(f"⏱️ Average duration: {avg_action_duration:.1f} minutes")

# Compare action movie durations with our threshold
short_action = action_movies_90s[action_movies_90s['duration'] <= duration_threshold]
long_action = action_movies_90s[action_movies_90s['duration'] > duration_threshold]

print(f"\n📏 Duration Analysis:")
print(f"   • Short action movies (≤{duration_threshold} min): {len(short_action)} ({len(short_action)/total_action_movies*100:.1f}%)")
print(f"   • Long action movies (>{duration_threshold} min): {len(long_action)} ({len(long_action)/total_action_movies*100:.1f}%)")

# Show notable action movies from different duration categories
print(f"\n🎯 Notable Examples:")
if len(short_action) > 0:
    shortest_action = short_action.loc[short_action['duration'].idxmin()]
    print(f"   • Shortest: '{shortest_action['title']}' ({shortest_action['duration']} min, {shortest_action['release_year']})")

if len(long_action) > 0:
    longest_action = long_action.loc[long_action['duration'].idxmax()]
    print(f"   • Longest: '{longest_action['title']}' ({longest_action['duration']} min, {longest_action['release_year']})")

# Display sample of action movies for exploration
print(f"\n🔍 Sample Action Movies from the 1990s:")
display_cols = ['title', 'release_year', 'duration', 'director', 'country']
print(action_movies_90s[display_cols].head().to_string())